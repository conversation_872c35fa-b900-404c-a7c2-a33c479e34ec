import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useSupabase } from "@/contexts/SupabaseContext";
import { useRealtimeMessage } from "./useRealtimeMessage";

// Interface cho expert data
export interface Expert {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar_url: string | null;
}

// Interface cho banner data
export interface Banner {
  id: string;
  expert: Expert;
  tournament: string;
  team_name: string;
  closing_bet: string;
  saying: string;
  status: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  sent_at: string | null;
  type: string;
  // Legacy fields for backward compatibility
  order?: string;
  displayed?: string;
  winDisplayed?: string;
  updatedAt?: string;
}

// Interface cho chat message
interface ChatMessage {
  id: string;
  content: string;
  created_at: string;
}

const bannerRoomId = "22dbb62a-8ff0-4095-bc8a-574106709db3";

export function useBanner() {
  const [currentBanner, setCurrentBanner] = useState<ChatMessage | null>(null);
  const [readMessageIds, setReadMessageIds] = useState<Set<string>>(new Set());
  const readMessageIdsRef = useRef<Set<string>>(new Set());
  const [filteredMessages, setFilteredMessages] = useState<ChatMessage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [authMode, setAuthMode] = useState<"login" | "register">("login");
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const { userJoinTime } = useSupabase();

  const { messages: banners } = useRealtimeMessage(bannerRoomId);

  // Load readMessageIds từ localStorage khi component mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("bannerReadMessageIds");
      if (stored) {
        const storedIds = new Set(JSON.parse(stored) as string[]);
        console.log("Loaded readMessageIds from localStorage:", storedIds);
        setReadMessageIds(storedIds);
        readMessageIdsRef.current = storedIds;
      }
    }
  }, []);

  // Lưu readMessageIds vào localStorage mỗi khi thay đổi
  useEffect(() => {
    if (typeof window !== "undefined" && readMessageIds.size > 0) {
      localStorage.setItem(
        "bannerReadMessageIds",
        JSON.stringify([...readMessageIds])
      );
    } else {
      console.log("Not saving to localStorage - size:", readMessageIds.size);
    }
  }, [readMessageIds]);

  // Lọc messages theo userJoinTime
  useEffect(() => {
    if (banners && userJoinTime) {
      const filtered = banners.filter((message) => {
        const messageTime = new Date(message.created_at).getTime();
        const joinTime = new Date(userJoinTime).getTime();
        return (
          messageTime >= joinTime && !readMessageIdsRef.current.has(message.id)
        );
      });
      setFilteredMessages(filtered);
      setCurrentIndex(0);
    }
  }, [banners, userJoinTime, readMessageIds]);

  // Hiển thị banner tiếp theo
  const showNextBanner = useCallback(() => {
    if (filteredMessages.length === 0) {
      setCurrentBanner(null);
      return;
    }

    if (currentIndex < filteredMessages.length) {
      const nextMessage = filteredMessages[currentIndex];
      setCurrentBanner(nextMessage);

      // Set timer để ẩn banner sau 15 giây
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      timerRef.current = setTimeout(() => {
        // Đánh dấu message đã đọc
        const newSet = new Set([...readMessageIdsRef.current, nextMessage.id]);
        readMessageIdsRef.current = newSet;
        setReadMessageIds(newSet);

        // Force save to localStorage immediately
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "bannerReadMessageIds",
            JSON.stringify([...newSet])
          );
        }
        setCurrentBanner(null);
        setCurrentIndex((prev) => prev + 1);
      }, 15000);
    } else {
      setCurrentBanner(null);
    }
  }, [filteredMessages, currentIndex]);

  // Bắt đầu hiển thị banner khi có messages mới
  useEffect(() => {
    if (
      filteredMessages.length > 0 &&
      currentIndex < filteredMessages.length &&
      !currentBanner
    ) {
      showNextBanner();
    }
  }, [filteredMessages, currentIndex, currentBanner, showNextBanner]);

  // Cleanup timer khi component unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);
  // Hàm để manually ẩn banner hiện tại
  const hideBanner = useCallback(() => {
    if (currentBanner) {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // Cập nhật cả useState và useRef
      const newSet = new Set([...readMessageIdsRef.current, currentBanner.id]);
      readMessageIdsRef.current = newSet;
      setReadMessageIds(newSet);

      // Force save to localStorage immediately
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "bannerReadMessageIds",
          JSON.stringify([...newSet])
        );
      }

      setCurrentBanner(null);
      setCurrentIndex((prev) => prev + 1);
    }
  }, [currentBanner, readMessageIds]);

  const openAuthModal = (mode: "login" | "register" = "login") => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const setAuthModalMode = (mode: "login" | "register") => {
    openAuthModal(mode);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
    setAuthMode("login");
  };

  const bannerDataParsed = useMemo(() => {
    if (!currentBanner) return null;
    try {
      const parsedData = JSON.parse(currentBanner?.content || "{}");
      const bannerId = currentBanner?.id || ""; //
      return {
        id: parsedData.id || bannerId,
        expert: {
          id: parsedData.expert?.id || parsedData.id || bannerId,
          name: parsedData.expert?.name || "ADMIN VĂN KHÁNH",
          role: parsedData.expert?.role || "admin",
          email: parsedData.expert?.email || "<EMAIL>",
          avatar_url: parsedData.expert?.avatar_url || null,
        },
        tournament: parsedData.tournament || "",
        team_name: parsedData.team_name || "",
        closing_bet: parsedData.closing_bet || "",
        saying: parsedData.saying || "",
        status: parsedData.status || null,
        created_at: parsedData.created_at || currentBanner?.created_at,
        updated_at: parsedData.updated_at || currentBanner?.created_at,
        created_by: parsedData.created_by || null,
        sent_at: parsedData.sent_at || null,
        type: parsedData.type || "KÈO VIP",
        // Legacy fields for backward compatibility
        order: bannerId,
        displayed: "false",
        winDisplayed: "false",
        updatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error parsing banner data:", error);
      return null;
    }
  }, [currentBanner]);

  return {
    banners,
    currentBanner,
    bannerData: bannerDataParsed,
    filteredMessages,
    readMessageIds,
    isAuthModalOpen,
    authMode,
    setAuthModalMode,
    openAuthModal,
    closeAuthModal,
    setAuthMode,
    hideBanner,
  };
}
